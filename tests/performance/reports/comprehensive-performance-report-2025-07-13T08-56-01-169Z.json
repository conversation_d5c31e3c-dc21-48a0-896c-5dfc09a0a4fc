{"testSuite": "Professional Large Document Performance Test Suite", "version": "2.0.0", "generatedAt": "2025-07-13T08:56:01.170Z", "serverUrl": "ws://localhost:3000", "configuration": {"outputDir": "/Users/<USER>/Desktop/realtime_yjs_server/tests/performance/reports", "verbose": true, "saveDetailedLogs": true, "serverUrl": "ws://localhost:3000"}, "summary": {"totalTests": 3, "successfulTests": 3, "failedTests": 0, "successRate": "100.00"}, "testResults": [{"config": {"size": 102400, "label": "100KB", "users": 5, "duration": 60000, "documentType": "report", "testType": "standard_load"}, "success": true, "totalTime": 67964, "documentSummary": {"size": 92173, "sizeFormatted": "0.09MB", "contentTypes": {"paragraphs": 622, "tables": 19, "lists": 35, "headings": 6}, "estimatedWords": 15362, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752396824523, "endTime": 1752396959155, "duration": 134632, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "1.1m", "durationMs": 67964, "totalUsers": 5, "successfulConnections": 5, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "12.37 MB", "peakBytes": 12974016, "average": "11.21 MB", "averageBytes": 11757297.23076923, "maxUsagePercent": "87.6%"}, "network": {"peakThroughput": "0 msg/s", "totalMessages": 0, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 5, "activeUsers": 5, "averageConnectionTime": "38.4ms", "averageConnectionTimeMs": 38.4, "totalEdits": 438, "averageEditsPerUser": 87.6, "userRoles": {"admin": 2, "editor": 1, "reviewer": 1, "contributor": 1}}, "documentMetrics": {"totalDocuments": 1, "totalOperations": 438, "averageOperationsPerDocument": 438}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T08:50:56.933Z"}, {"config": {"size": 1572864, "label": "1.5MB", "users": 10, "duration": 90000, "documentType": "proposal", "testType": "medium_load"}, "success": true, "totalTime": 103529, "documentSummary": {"size": 689705, "sizeFormatted": "0.66MB", "contentTypes": {"paragraphs": 4659, "tables": 139, "lists": 251, "headings": 6}, "estimatedWords": 114950, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752396824523, "endTime": 1752396959155, "duration": 134632, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "1.7m", "durationMs": 103529, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "24.51 MB", "peakBytes": 25702560, "average": "17.33 MB", "averageBytes": 18166707.393939395, "maxUsagePercent": "92.4%"}, "network": {"peakThroughput": "240 msg/s", "totalMessages": 23960, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "62343.2ms", "averageConnectionTimeMs": 62343.2, "totalEdits": 571, "averageEditsPerUser": 57.1, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 2, "totalOperations": 1009, "averageOperationsPerDocument": 504.5}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T08:53:12.492Z"}, {"config": {"size": 2097152, "label": "2MB", "users": 10, "duration": 120000, "documentType": "specification", "testType": "heavy_load"}, "success": true, "totalTime": 134632, "documentSummary": {"size": 651218, "sizeFormatted": "0.62MB", "contentTypes": {"paragraphs": 4259, "tables": 113, "lists": 276, "headings": 5}, "estimatedWords": 108536, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752396824523, "endTime": 1752396959155, "duration": 134632, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134632, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "28.63 MB", "peakBytes": 30017376, "average": "20.45 MB", "averageBytes": 21440908.33898305, "maxUsagePercent": "93.2%"}, "network": {"peakThroughput": "429 msg/s", "totalMessages": 79678, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "40.9ms", "averageConnectionTimeMs": 40.9, "totalEdits": 1419, "averageEditsPerUser": 141.9, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2428, "averageOperationsPerDocument": 809.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T08:55:59.155Z"}], "performanceCollectorReport": {"testSession": {"startTime": 1752396824523, "endTime": 1752396959155, "duration": 134632, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134632, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "28.63 MB", "peakBytes": 30017376, "average": "20.45 MB", "averageBytes": 21440908.33898305, "maxUsagePercent": "93.2%"}, "network": {"peakThroughput": "429 msg/s", "totalMessages": 79678, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "40.9ms", "averageConnectionTimeMs": 40.9, "totalEdits": 1419, "averageEditsPerUser": 141.9, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2428, "averageOperationsPerDocument": 809.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}}