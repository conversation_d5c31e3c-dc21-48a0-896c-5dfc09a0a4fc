{"testSession": {"startTime": 1752396824523, "endTime": 1752396959155, "duration": 134632, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134632, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "28.63 MB", "peakBytes": 30017376, "average": "20.45 MB", "averageBytes": 21440908.33898305, "maxUsagePercent": "93.2%"}, "network": {"peakThroughput": "429 msg/s", "totalMessages": 79678, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "40.9ms", "averageConnectionTimeMs": 40.9, "totalEdits": 1419, "averageEditsPerUser": 141.9, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2428, "averageOperationsPerDocument": 809.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}