{"config": {"size": 2097152, "label": "2MB", "users": 10, "duration": 120000, "documentType": "specification", "testType": "heavy_load"}, "success": true, "totalTime": 134632, "documentSummary": {"size": 651218, "sizeFormatted": "0.62MB", "contentTypes": {"paragraphs": 4259, "tables": 113, "lists": 276, "headings": 5}, "estimatedWords": 108536, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752396824523, "endTime": 1752396959155, "duration": 134632, "testType": "heavy_load", "configuration": {"testType": "heavy_load", "documentSize": 2097152, "userCount": 10, "duration": 120000, "documentType": "specification"}}, "summary": {"duration": "2.2m", "durationMs": 134632, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "28.63 MB", "peakBytes": 30017376, "average": "20.45 MB", "averageBytes": 21440908.33898305, "maxUsagePercent": "93.2%"}, "network": {"peakThroughput": "429 msg/s", "totalMessages": 79678, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "40.9ms", "averageConnectionTimeMs": 40.9, "totalEdits": 1419, "averageEditsPerUser": 141.9, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 3, "totalOperations": 2428, "averageOperationsPerDocument": 809.3333333333334}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T08:55:59.155Z", "generatedAt": "2025-07-13T08:56:01.164Z", "testSuite": "Professional Large Document Performance Test", "version": "2.0.0"}