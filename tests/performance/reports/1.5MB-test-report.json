{"config": {"size": 1572864, "label": "1.5MB", "users": 10, "duration": 90000, "documentType": "proposal", "testType": "medium_load"}, "success": true, "totalTime": 103529, "documentSummary": {"size": 689705, "sizeFormatted": "0.66MB", "contentTypes": {"paragraphs": 4659, "tables": 139, "lists": 251, "headings": 6}, "estimatedWords": 114950, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752396688962, "endTime": 1752396792491, "duration": 103529, "testType": "medium_load", "configuration": {"testType": "medium_load", "documentSize": 1572864, "userCount": 10, "duration": 90000, "documentType": "proposal"}}, "summary": {"duration": "1.7m", "durationMs": 103529, "totalUsers": 10, "successfulConnections": 10, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "24.51 MB", "peakBytes": 25702560, "average": "17.33 MB", "averageBytes": 18166707.393939395, "maxUsagePercent": "92.4%"}, "network": {"peakThroughput": "240 msg/s", "totalMessages": 23960, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 10, "activeUsers": 10, "averageConnectionTime": "62343.2ms", "averageConnectionTimeMs": 62343.2, "totalEdits": 571, "averageEditsPerUser": 57.1, "userRoles": {"admin": 3, "editor": 3, "reviewer": 2, "contributor": 2}}, "documentMetrics": {"totalDocuments": 2, "totalOperations": 1009, "averageOperationsPerDocument": 504.5}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T08:53:12.492Z", "generatedAt": "2025-07-13T08:53:14.519Z", "testSuite": "Professional Large Document Performance Test", "version": "2.0.0"}