{"config": {"size": 102400, "label": "100KB", "users": 5, "duration": 60000, "documentType": "report", "testType": "standard_load"}, "success": true, "totalTime": 67964, "documentSummary": {"size": 92173, "sizeFormatted": "0.09MB", "contentTypes": {"paragraphs": 622, "tables": 19, "lists": 35, "headings": 6}, "estimatedWords": 15362, "complexity": "Very Complex"}, "performanceReport": {"testSession": {"startTime": 1752396588969, "endTime": 1752396656933, "duration": 67964, "testType": "standard_load", "configuration": {"testType": "standard_load", "documentSize": 102400, "userCount": 5, "duration": 60000, "documentType": "report"}}, "summary": {"duration": "1.1m", "durationMs": 67964, "totalUsers": 5, "successfulConnections": 5, "connectionSuccessRate": 1, "totalOperations": 0, "averageLatency": "0ms", "averageLatencyMs": 0, "totalErrors": 0, "errorRate": "0%", "alertCount": 0}, "systemMetrics": {"memory": {"peak": "12.37 MB", "peakBytes": 12974016, "average": "11.21 MB", "averageBytes": 11757297.23076923, "maxUsagePercent": "87.6%"}, "network": {"peakThroughput": "0 msg/s", "totalMessages": 0, "totalBytes": "0 B"}}, "applicationMetrics": {"latency": {"min": 0, "max": 0, "average": 0, "p95": 0, "p99": 0}, "throughput": 0, "reliability": 100}, "userMetrics": {"totalUsers": 5, "activeUsers": 5, "averageConnectionTime": "38.4ms", "averageConnectionTimeMs": 38.4, "totalEdits": 438, "averageEditsPerUser": 87.6, "userRoles": {"admin": 2, "editor": 1, "reviewer": 1, "contributor": 1}}, "documentMetrics": {"totalDocuments": 1, "totalOperations": 438, "averageOperationsPerDocument": 438}, "alerts": {"total": 0, "byType": {}, "bySeverity": {}, "recent": []}, "recommendations": [{"type": "memory", "priority": "medium", "message": "High memory usage detected", "suggestion": "Enable garbage collection optimization or increase memory limits"}]}, "timestamp": "2025-07-13T08:50:56.933Z", "generatedAt": "2025-07-13T08:50:58.957Z", "testSuite": "Professional Large Document Performance Test", "version": "2.0.0"}