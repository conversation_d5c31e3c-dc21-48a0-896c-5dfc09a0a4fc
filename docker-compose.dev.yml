version: '3.8'

services:
  # Redis service for caching and session management
  redis:
    image: redis:7.4-alpine
    container_name: realtime-yjs-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Main application service for development
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: realtime-yjs-server-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      - AUTH_TEST_MODE=${AUTH_TEST_MODE:-false}
      - DEBOUNCE_ENABLED=${DEBOUNCE_ENABLED:-true}
      - DEBOUNCE_DELAY=${DEBOUNCE_DELAY:-300}
      - DEBOUNCE_MAX_DELAY=${DEBOUNCE_MAX_DELAY:-1000}
      - ENABLE_NODE_DEBUG=${ENABLE_NODE_DEBUG:-0}
      - ENABLE_OPTIMIZATIONS=${ENABLE_OPTIMIZATIONS:-true}
      - NODE_OPTIONS=--max-old-space-size=2048 --expose-gc
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      # Mount source code for hot reloading
      - ./src:/app/src:cached
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - ./logs:/app/logs
      # Use named volume for node_modules to persist across rebuilds
      - server_node_modules:/app/node_modules
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { if (res.statusCode === 200) process.exit(0); else process.exit(1); }).on('error', () => process.exit(1));"]
      interval: 30s
      timeout: 3s
      retries: 3

  # React client service for development
  client:
    build:
      context: ./client
      dockerfile: Dockerfile.dev
    container_name: realtime-yjs-client-dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://app:3000
      - VITE_WS_URL=ws://app:3000
    depends_on:
      - app
    restart: unless-stopped
    volumes:
      # Mount source code for hot reloading
      - ./client/src:/app/src:cached
      - ./client/index.html:/app/index.html:cached
      - ./client/vite.config.js:/app/vite.config.js:ro
      - ./client/package.json:/app/package.json:ro
      - ./client/package-lock.json:/app/package-lock.json:ro
      # Use named volume for node_modules to persist across rebuilds
      - client_node_modules:/app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/"]
      interval: 30s
      timeout: 3s
      retries: 3

volumes:
  redis_data_dev:
  client_node_modules:
  server_node_modules:
