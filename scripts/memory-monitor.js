#!/usr/bin/env node

/**
 * Memory Monitoring Script for Y.js Collaborative Server
 * Usage: node scripts/memory-monitor.js [options]
 */

const http = require('http');

class MemoryMonitor {
  constructor(options = {}) {
    this.serverUrl = options.serverUrl || 'http://localhost:3000';
    this.interval = options.interval || 10000; // 10 seconds
    this.threshold = options.threshold || 85; // 85% memory usage
    this.autoGC = options.autoGC || false;
    this.running = false;
  }

  async getMemoryStats() {
    return new Promise((resolve, reject) => {
      const req = http.get(`${this.serverUrl}/api/memory`, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  async triggerGC() {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify({});
      const options = {
        hostname: new URL(this.serverUrl).hostname,
        port: new URL(this.serverUrl).port || 3000,
        path: '/api/gc',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', reject);
      req.write(postData);
      req.end();
    });
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async monitor() {
    try {
      const stats = await this.getMemoryStats();
      const usagePercent = parseFloat(stats.heap.usagePercent);
      const timestamp = new Date().toISOString();

      console.log(`[${timestamp}] Memory Usage: ${stats.heap.used}/${stats.heap.total} (${stats.heap.usagePercent})`);
      console.log(`  RSS: ${stats.rss}, External: ${stats.external}, Array Buffers: ${stats.arrayBuffers}`);
      
      if (stats.memoryManager) {
        console.log(`  Documents Cached: ${stats.memoryManager.cache.documentCount}/${stats.memoryManager.cache.maxSize}`);
        console.log(`  GC Count: ${stats.memoryManager.gc.count}, Last GC: ${stats.memoryManager.gc.lastRun || 'Never'}`);
      }

      // Check if memory usage is above threshold
      if (usagePercent > this.threshold) {
        console.log(`⚠️  HIGH MEMORY USAGE: ${usagePercent}% (threshold: ${this.threshold}%)`);
        
        if (this.autoGC) {
          console.log('🧹 Triggering automatic garbage collection...');
          try {
            const gcResult = await this.triggerGC();
            console.log(`✅ GC triggered: ${gcResult.message}`);
          } catch (error) {
            console.log(`❌ GC failed: ${error.message}`);
          }
        } else {
          console.log('💡 Recommendation: Run "curl -X POST http://localhost:3000/api/gc" to trigger garbage collection');
        }
      }

      console.log('---');
    } catch (error) {
      console.error(`❌ Failed to get memory stats: ${error.message}`);
    }
  }

  start() {
    if (this.running) {
      console.log('Monitor is already running');
      return;
    }

    this.running = true;
    console.log(`🔍 Starting memory monitor (interval: ${this.interval}ms, threshold: ${this.threshold}%)`);
    console.log(`📊 Server: ${this.serverUrl}`);
    console.log(`🤖 Auto GC: ${this.autoGC ? 'Enabled' : 'Disabled'}`);
    console.log('---');

    // Initial check
    this.monitor();

    // Set up interval
    this.intervalId = setInterval(() => {
      this.monitor();
    }, this.interval);

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      this.stop();
      process.exit(0);
    });
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.running = false;
    console.log('🛑 Memory monitor stopped');
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--url':
        options.serverUrl = args[++i];
        break;
      case '--interval':
        options.interval = parseInt(args[++i]) * 1000;
        break;
      case '--threshold':
        options.threshold = parseInt(args[++i]);
        break;
      case '--auto-gc':
        options.autoGC = true;
        break;
      case '--help':
        console.log(`
Memory Monitor for Y.js Collaborative Server

Usage: node scripts/memory-monitor.js [options]

Options:
  --url <url>         Server URL (default: http://localhost:3000)
  --interval <sec>    Check interval in seconds (default: 10)
  --threshold <pct>   Memory threshold percentage (default: 85)
  --auto-gc          Automatically trigger GC when threshold exceeded
  --help             Show this help message

Examples:
  node scripts/memory-monitor.js
  node scripts/memory-monitor.js --interval 5 --threshold 80
  node scripts/memory-monitor.js --auto-gc --threshold 90
        `);
        process.exit(0);
        break;
    }
  }

  const monitor = new MemoryMonitor(options);
  monitor.start();
}

module.exports = MemoryMonitor;
