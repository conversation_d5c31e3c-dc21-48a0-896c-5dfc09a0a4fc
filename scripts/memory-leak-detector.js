#!/usr/bin/env node

/**
 * Memory Leak Detection Script for Y.js Collaborative Server
 * Monitors for potential memory leaks and provides detailed analysis
 */

const http = require('http');

class MemoryLeakDetector {
  constructor(options = {}) {
    this.serverUrl = options.serverUrl || 'http://localhost:3000';
    this.interval = options.interval || 30000; // 30 seconds
    this.samples = options.samples || 20; // Number of samples to collect
    this.threshold = options.threshold || 10; // MB growth threshold
    this.running = false;
    this.memoryHistory = [];
    this.leakDetected = false;
  }

  async getMemoryStats() {
    return new Promise((resolve, reject) => {
      const req = http.get(`${this.serverUrl}/api/memory`, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  async getServerStats() {
    return new Promise((resolve, reject) => {
      const req = http.get(`${this.serverUrl}/api/stats`, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  parseMemoryValue(memStr) {
    const match = memStr.match(/^([\d.]+)(MB|GB|KB)$/);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 'GB': return value * 1024;
      case 'MB': return value;
      case 'KB': return value / 1024;
      default: return value;
    }
  }

  async collectSample() {
    try {
      const [memStats, serverStats] = await Promise.all([
        this.getMemoryStats(),
        this.getServerStats()
      ]);

      const sample = {
        timestamp: Date.now(),
        heapUsed: this.parseMemoryValue(memStats.heap.used),
        heapTotal: this.parseMemoryValue(memStats.heap.total),
        rss: this.parseMemoryValue(memStats.rss),
        external: this.parseMemoryValue(memStats.external),
        arrayBuffers: this.parseMemoryValue(memStats.arrayBuffers),
        uptime: parseInt(memStats.uptime.replace('s', '')),
        connections: serverStats.connections?.total || 0,
        documents: serverStats.documents?.total || 0,
        memoryManager: memStats.memoryManager || null
      };

      this.memoryHistory.push(sample);

      // Keep only the last N samples
      if (this.memoryHistory.length > this.samples) {
        this.memoryHistory.shift();
      }

      return sample;
    } catch (error) {
      console.error(`❌ Failed to collect memory sample: ${error.message}`);
      return null;
    }
  }

  analyzeMemoryTrend() {
    if (this.memoryHistory.length < 5) {
      return { trend: 'insufficient_data', growth: 0 };
    }

    const recent = this.memoryHistory.slice(-5);
    const oldest = recent[0];
    const newest = recent[recent.length - 1];

    const heapGrowth = newest.heapUsed - oldest.heapUsed;
    const rssGrowth = newest.rss - oldest.rss;
    const timeSpan = (newest.timestamp - oldest.timestamp) / 1000; // seconds

    // Calculate growth rate per minute
    const heapGrowthRate = (heapGrowth / timeSpan) * 60;
    const rssGrowthRate = (rssGrowth / timeSpan) * 60;

    let trend = 'stable';
    if (heapGrowthRate > 1 || rssGrowthRate > 1) { // > 1MB/min
      trend = 'growing';
    } else if (heapGrowthRate < -1 || rssGrowthRate < -1) {
      trend = 'decreasing';
    }

    return {
      trend,
      heapGrowth: heapGrowth.toFixed(2),
      rssGrowth: rssGrowth.toFixed(2),
      heapGrowthRate: heapGrowthRate.toFixed(2),
      rssGrowthRate: rssGrowthRate.toFixed(2),
      timeSpan: timeSpan.toFixed(0)
    };
  }

  detectMemoryLeaks() {
    const analysis = this.analyzeMemoryTrend();
    const latest = this.memoryHistory[this.memoryHistory.length - 1];

    const issues = [];

    // Check for consistent memory growth
    if (analysis.trend === 'growing' && parseFloat(analysis.heapGrowthRate) > 2) {
      issues.push({
        type: 'heap_growth',
        severity: 'warning',
        message: `Heap memory growing at ${analysis.heapGrowthRate}MB/min`,
        recommendation: 'Check for memory leaks in event listeners, intervals, or cached data'
      });
    }

    // Check for high RSS growth
    if (analysis.trend === 'growing' && parseFloat(analysis.rssGrowthRate) > 5) {
      issues.push({
        type: 'rss_growth',
        severity: 'critical',
        message: `RSS memory growing at ${analysis.rssGrowthRate}MB/min`,
        recommendation: 'Possible memory leak in native modules or large object retention'
      });
    }

    // Check for high external memory
    if (latest.external > 100) {
      issues.push({
        type: 'external_memory',
        severity: 'warning',
        message: `High external memory usage: ${latest.external.toFixed(2)}MB`,
        recommendation: 'Check for large buffers or external resources not being freed'
      });
    }

    // Check for high array buffer usage
    if (latest.arrayBuffers > 50) {
      issues.push({
        type: 'array_buffers',
        severity: 'warning',
        message: `High ArrayBuffer usage: ${latest.arrayBuffers.toFixed(2)}MB`,
        recommendation: 'Check Y.js document updates and WebSocket message handling'
      });
    }

    // Check memory manager efficiency
    if (latest.memoryManager) {
      const gcCount = latest.memoryManager.gc?.count || 0;
      const docCount = latest.memoryManager.cache?.documentCount || 0;
      
      if (docCount > 25 && gcCount < 5) {
        issues.push({
          type: 'gc_efficiency',
          severity: 'info',
          message: `Low GC activity with ${docCount} cached documents`,
          recommendation: 'Consider triggering manual garbage collection'
        });
      }
    }

    return issues;
  }

  displayReport(sample, analysis, issues) {
    const timestamp = new Date(sample.timestamp).toISOString();
    
    console.log(`\n📊 Memory Leak Analysis Report - ${timestamp}`);
    console.log('=' .repeat(60));
    
    console.log(`\n💾 Current Memory Usage:`);
    console.log(`  Heap: ${sample.heapUsed.toFixed(2)}MB / ${sample.heapTotal.toFixed(2)}MB`);
    console.log(`  RSS: ${sample.rss.toFixed(2)}MB`);
    console.log(`  External: ${sample.external.toFixed(2)}MB`);
    console.log(`  Array Buffers: ${sample.arrayBuffers.toFixed(2)}MB`);
    
    console.log(`\n📈 Memory Trend (last 5 samples):`);
    console.log(`  Trend: ${analysis.trend.toUpperCase()}`);
    console.log(`  Heap Growth: ${analysis.heapGrowth}MB (${analysis.heapGrowthRate}MB/min)`);
    console.log(`  RSS Growth: ${analysis.rssGrowth}MB (${analysis.rssGrowthRate}MB/min)`);
    
    console.log(`\n🔗 Server State:`);
    console.log(`  Connections: ${sample.connections}`);
    console.log(`  Documents: ${sample.documents}`);
    console.log(`  Uptime: ${sample.uptime}s`);

    if (sample.memoryManager) {
      console.log(`\n🧹 Memory Manager:`);
      console.log(`  Documents Cached: ${sample.memoryManager.cache?.documentCount || 0}`);
      console.log(`  GC Count: ${sample.memoryManager.gc?.count || 0}`);
      console.log(`  Last GC: ${sample.memoryManager.gc?.lastRun || 'Never'}`);
    }

    if (issues.length > 0) {
      console.log(`\n⚠️  Potential Issues Detected:`);
      issues.forEach((issue, index) => {
        const icon = issue.severity === 'critical' ? '🚨' : 
                    issue.severity === 'warning' ? '⚠️' : '💡';
        console.log(`  ${icon} ${issue.type.toUpperCase()}: ${issue.message}`);
        console.log(`     💡 ${issue.recommendation}`);
      });
      
      if (!this.leakDetected) {
        this.leakDetected = true;
        console.log(`\n🔧 Suggested Actions:`);
        console.log(`  1. Run: curl -X POST ${this.serverUrl}/api/gc`);
        console.log(`  2. Run: curl -X POST ${this.serverUrl}/api/cleanup/documents`);
        console.log(`  3. Monitor for continued growth`);
      }
    } else {
      console.log(`\n✅ No memory leaks detected`);
      this.leakDetected = false;
    }
    
    console.log('=' .repeat(60));
  }

  async start() {
    if (this.running) {
      console.log('Memory leak detector is already running');
      return;
    }

    this.running = true;
    console.log(`🔍 Starting memory leak detection`);
    console.log(`📊 Server: ${this.serverUrl}`);
    console.log(`⏱️  Interval: ${this.interval / 1000}s`);
    console.log(`📈 Samples: ${this.samples}`);
    console.log(`🚨 Growth threshold: ${this.threshold}MB`);

    // Initial sample
    await this.collectSample();

    // Set up interval
    this.intervalId = setInterval(async () => {
      const sample = await this.collectSample();
      if (sample) {
        const analysis = this.analyzeMemoryTrend();
        const issues = this.detectMemoryLeaks();
        this.displayReport(sample, analysis, issues);
      }
    }, this.interval);

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      this.stop();
      process.exit(0);
    });
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.running = false;
    console.log('\n🛑 Memory leak detector stopped');
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--url':
        options.serverUrl = args[++i];
        break;
      case '--interval':
        options.interval = parseInt(args[++i]) * 1000;
        break;
      case '--samples':
        options.samples = parseInt(args[++i]);
        break;
      case '--threshold':
        options.threshold = parseInt(args[++i]);
        break;
      case '--help':
        console.log(`
Memory Leak Detector for Y.js Collaborative Server

Usage: node scripts/memory-leak-detector.js [options]

Options:
  --url <url>         Server URL (default: http://localhost:3000)
  --interval <sec>    Check interval in seconds (default: 30)
  --samples <num>     Number of samples to keep (default: 20)
  --threshold <mb>    Growth threshold in MB (default: 10)
  --help             Show this help message

Examples:
  node scripts/memory-leak-detector.js
  node scripts/memory-leak-detector.js --interval 60 --samples 30
        `);
        process.exit(0);
        break;
    }
  }

  const detector = new MemoryLeakDetector(options);
  detector.start();
}

module.exports = MemoryLeakDetector;
