{"name": "tiptap-collaborative-server", "version": "1.0.0", "description": "A real-time collaborative rich text editor server using Tiptap, YJS and y-websocket with SOLID principles", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "docker:build": "docker build -t realtime-yjs-server .", "docker:run": "docker run -p 3000:3000 realtime-yjs-server", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:dev:logs": "docker-compose -f docker-compose.dev.yml logs -f app", "docker:prod": "docker-compose up --build", "docker:prod:down": "docker-compose down", "perf-test": "node tests/performance/runPerformanceTest.js", "perf-test:quick": "USER_COUNT=10 TEST_DURATION=30000 node tests/performance/runPerformanceTest.js", "perf-test:stress": "USER_COUNT=50 TEST_DURATION=90000 KEYSTROKE_INTERVAL=300 node tests/performance/runPerformanceTest.js", "perf-test:stress-no-debounce": "USER_COUNT=50 TEST_DURATION=90000 KEYSTROKE_INTERVAL=300 DEBOUNCE_ENABLED=false node tests/performance/runPerformanceTest.js", "test:debounce": "node tests/debounce-test.js", "verify:redis-sync": "node scripts/verify-redis-sync.js"}, "keywords": ["tiptap", "rich-text-editor", "yjs", "y-websocket", "realtime", "collaboration", "websocket", "nodejs", "prose<PERSON><PERSON>r"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "ioredis": "^5.6.1", "js-base64": "^3.7.7", "jsonwebtoken": "^9.0.2", "lib0": "^0.2.109", "redis": "^5.5.6", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.18.3", "y-websocket": "^1.5.0", "yjs": "^13.6.27"}, "devDependencies": {"@jest/globals": "^29.7.0", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=22.7.0"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/index.js", "!**/node_modules/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}